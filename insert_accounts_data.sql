-- Insert admin account data into accounts table (PostgreSQL compatible)
INSERT INTO accounts (
    id,
    name,
    email,
    password,
    password_salt,
    avatar,
    interface_language,
    interface_theme,
    timezone,
    last_login_at,
    last_login_ip,
    status,
    initialized_at,
    created_at,
    updated_at,
    last_active_at
) VALUES (
    '1B65B344D66A0D56E063020011ACD734'::uuid,
    '<EMAIL>',
    '<EMAIL>',
    'YTczYjI4NTNiMGY1MDU2OTliYzQyOGE2NjVkNGZhYzE5Njk3YWE1NzAxMWE1MjhhMDMzMDM5MjM4ZjdkZWZiZA==',
    '+gA2CA/FQorzXBC3H2HvCw==',
    NULL,
    'zh-Hans',
    'light',
    'Asia/Shanghai',
    '2025-05-30 10:06:14'::timestamp,
    '127.0.0.1',
    'active',
    '2024-06-21 11:26:18'::timestamp,
    '2024-06-21 11:26:19'::timestamp,
    '2024-06-21 11:26:19'::timestamp,
    '2025-06-03 10:16:41'::timestamp
);
